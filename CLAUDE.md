# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
pnpm install              # Install dependencies
pnpm dev                  # Start both web and server in development
pnpm dev:web             # Start only the web app (port 3001)
pnpm dev:server          # Start only the server (port 3000)
pnpm build               # Build all applications
pnpm check-types         # TypeScript type checking across all apps
pnpm lint                # Run Biome linting
pnpm lint:fix            # Run Biome linting with auto-fix
pnpm format              # Format code with Biome
```

## Database Commands

```bash
pnpm db:push             # Push schema changes to database
pnpm db:studio           # Open Prisma Studio UI
pnpm db:generate         # Generate Prisma client
pnpm db:migrate          # Run database migrations
pnpm db:seed             # Seed database with initial data
pnpm db:reset            # Reset database and run migrations
pnpm db:sync-users       # Sync Clerk users to database
```

## Test Commands

```bash
# Server Tests (Vitest)
pnpm test               # Run all tests in watch mode
pnpm test:run           # Run all tests once
pnpm test:unit          # Run only unit tests
pnpm test:unit:run      # Run unit tests once
pnpm test:coverage      # Generate test coverage report
pnpm test:ui            # Open Vitest UI
pnpm test:setup         # Setup test database

# Individual test execution
pnpm test auth-flows    # Run specific test file
pnpm test --grep "user creation"  # Run tests matching pattern
```

## Project Architecture

This is a **Turborepo monorepo** with two main applications:

### Apps Structure
- **`apps/web/`** - Next.js frontend application (port 3001)
  - Uses shadcn/ui components with Tailwind CSS
  - Implements tRPC client for type-safe API calls
  - Uses TanStack Query for state management
- **`apps/server/`** - Next.js API server (port 3000)  
  - Serves tRPC API endpoints at `/trpc`
  - Uses Prisma ORM for database operations
  - PostgreSQL database with custom schema location

### Key Technologies
- **Frontend**: Next.js 15, React 19, shadcn/ui, TailwindCSS 4
- **Backend**: Next.js 15, tRPC 11, Prisma 6
- **Database**: PostgreSQL with Prisma ORM
- **Build System**: Turborepo, PNPM workspaces
- **Type Safety**: Full-stack TypeScript with tRPC
- **Error Monitoring**: Sentry for both web and server apps
- **Code Quality**: Biome for linting and formatting

## Important Configuration Details

### Database Setup
- Prisma schema is located at `apps/server/prisma/schema/schema.prisma` (not the default location)
- Generated client outputs to `apps/server/prisma/generated/`
- Requires `DATABASE_URL` and `DIRECT_URL` environment variables in `apps/server/.env`

### tRPC Setup
- Server router defined in `apps/server/src/routers/index.ts`
- Client configuration in `apps/web/src/utils/trpc.ts`
- Uses HTTP batch linking for optimal performance
- Error handling integrated with Sonner toast notifications

### Development Workflow
- Both apps run concurrently during development
- Web app uses Turbopack for fast builds
- Type checking is shared across the monorepo
- Database changes require pushing schema with `pnpm db:push`

### UI Component System
- Uses shadcn/ui with "new-york" style
- Tailwind CSS 4 with CSS variables
- Lucide React for icons
- Theme support with next-themes

### Error Monitoring (Sentry)
- **Web App**: `buddychip-web` project with session replay enabled
- **Server App**: `buddychip-server` project for API error tracking
- **Configuration**: Environment variables set in `.env` files
- **Error Boundary**: React error boundary component available at `apps/web/src/components/error-boundary.tsx`

### Code Quality (Biome)
- **Configuration**: `biome.json` in project root
- **Formatting**: 2-space indentation, semicolons, trailing commas
- **Linting**: TypeScript, React, accessibility, and performance rules
- **Integration**: Configured in all package.json files and Turborepo tasks

### Authentication (Clerk)
- **Provider**: Clerk for user authentication and management
- **Integration**: Both frontend (`@clerk/nextjs`) and backend (`@clerk/backend`)
- **Webhooks**: User sync webhook at `/api/webhooks/clerk` for database synchronization
- **Environment Variables**: Requires `CLERK_SECRET_KEY` and `CLERK_WEBHOOK_SIGNING_SECRET`
- **User Sync**: Manual sync available via `pnpm db:sync-users` script
- **Testing**: Uses `@clerk/testing` for mocking authentication in tests

### AI Agent System (Benji)
- **Framework**: Vercel AI SDK with streaming support
- **Providers**: OpenRouter and OpenAI via `@openrouter/ai-sdk-provider` and `@ai-sdk/openai`
- **Model Selection**: Dynamic based on user subscription plan (Free/Pro/Enterprise)
- **Tools Available**:
  - Exa Search (`exa-search.ts`) - Web search and content retrieval
  - XAI Search (`xai-search.ts`) - X/Twitter specific search
  - OpenAI Image (`openai-image.ts`) - Image generation capabilities
- **Context Management**: Conversation context persisted and managed
- **Rate Limiting**: Upstash rate limiting integrated

## Environment Variables

### Required for Server (`apps/server/.env`)
```env
# Database
DATABASE_URL=postgresql://...
DIRECT_URL=postgresql://...

# Authentication
CLERK_SECRET_KEY=sk_...
CLERK_WEBHOOK_SIGNING_SECRET=whsec_...

# AI Providers
OPENROUTER_API_KEY=sk-or-...
OPENAI_API_KEY=sk-...
XAI_API_KEY=xai-...
PERPLEXITY_API_KEY=pplx-...
EXA_API_KEY=...

# External Services
TWITTER_API_KEY=...
UPLOADTHING_TOKEN=...
MEM0_API_KEY=...

# Rate Limiting & Cache
KV_URL=...
KV_TOKEN=...

# Monitoring
SENTRY_AUTH_TOKEN=...

# Development Flags
VERBOSE_LOGGING=true
ENABLE_PRISMA_QUERY_LOGS=true
ENABLE_CONTEXT_LOGS=true
ENABLE_TRPC_REQUEST_LOGS=true
```

### Required for Web (`apps/web/.env`)
```env
# Clerk Authentication (Public)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_...

# API Configuration
OR_SITE_URL=https://...
OR_APP_NAME=BuddyChip
CORS_ORIGIN=http://localhost:3001
```

## Project-Specific Scripts

### Database Management
- `pnpm db:seed` - Populate database with initial subscription plans and test data
- `pnpm db:sync-users` - Sync Clerk users to local database (useful after authentication setup)
- `pnpm db:reset` - Reset database and run fresh migrations

### Development Utilities
- `fix-current-user.ts` - Script to fix user data inconsistencies
- `seed-subscription-plans.ts` - Initialize subscription tiers (Free, Pro, Enterprise)

### Testing Setup
- Vitest configured with separate unit and integration test configs
- MSW (Mock Service Worker) for API mocking in tests
- Clerk testing utilities for authentication mocking
- Test database isolation with single-threaded execution