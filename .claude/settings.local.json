{"permissions": {"allow": ["mcp__<PERSON><PERSON>__whoami", "mcp__Sentry__find_organizations", "mcp__Sentry__find_teams", "mcp__Sentry__find_projects", "mcp__<PERSON><PERSON>__create_project", "Bash(pnpm add:*)", "Bash(pnpm format:*)", "Bash(pnpm lint:*)", "Bash(find:*)", "mcp__context7__resolve-library-id", "mcp__context7__resolve-library-id", "mcp__exa__web_search_exa", "mcp__context7__get-library-docs", "<PERSON><PERSON>(touch:*)", "Bash(pnpm db:generate:*)", "Bash(pnpm check-types:*)", "Bash(pnpm build:*)", "Bash(pnpm update:*)", "Bash(cp:*)", "Bash(rm:*)", "Bash(pnpm db:seed:*)", "Bash(ls:*)", "Bash(npx prisma db push:*)", "Bash(node:*)", "Bash(npx prisma db pull:*)", "<PERSON><PERSON>(curl:*)", "Bash(pnpm db:push:*)", "<PERSON><PERSON>(timeout:*)", "Bash(pnpm run:*)", "Bash(npx prisma studio:*)", "WebFetch(domain:docs.twitterapi.io)", "WebFetch(domain:docs.x.ai)", "WebFetch(domain:docs.exa.ai)", "WebFetch(domain:platform.openai.com)", "WebFetch(domain:docs.mem0.ai)", "WebFetch(domain:docs.uploadthing.com)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm dev:server:*)", "Bash(pnpm db:studio:*)", "Bash(npx tsx:*)", "Bash(pnpm prisma:*)", "Bash(pnpm db:sync-users:*)", "<PERSON><PERSON>(tsx:*)", "Bash(grep:*)", "Bash(git stash push:*)", "Bash(git pull:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git stash:*)", "Bash(git add:*)", "Bash(npx tsc:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(pnpm --filter=web check-types)", "Bash(git reset:*)", "Bash(SENTRY_SUPPRESS_GLOBAL_ERROR_HANDLER_FILE_WARNING=1 pnpm build)", "mcp__docker-gateway__perplexity_research", "Bash(npm ls:*)", "Bash(git rm:*)", "mcp__docker-gateway__firecrawl_search", "<PERSON><PERSON>(vercel:*)", "Bash(git remote get-url:*)", "mcp__docker-gateway__browser_navigate", "mcp__docker-gateway__browser_click", "<PERSON><PERSON>(cat:*)", "Bash(pnpm exec prisma:*)", "Bash(pnpm install:*)", "Bash(pnpm test:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pnpm dev:web:*)", "Bash(pnpm dev:*)", "Bash(pnpm turbo build:*)"], "deny": []}}