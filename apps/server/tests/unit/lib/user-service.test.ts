/**
 * Unit tests for User Service
 * 
 * Tests user service functions including user creation, retrieval,
 * usage tracking, and feature access control
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { 
  getOrCreateUser, 
  getUserById, 
  updateUser, 
  getUserUsage, 
  canUserUseFeature, 
  logUsage 
} from '@/lib/user-service'
import { testDb, seedTestData } from '../../helpers/db-helpers'
import { createTestUserInDb, createTestUsageLogInDb } from '../../helpers/test-data'

describe('User Service', () => {
  beforeEach(async () => {
    console.log('🧪 Setting up user service test...')
    await seedTestData()
  })

  afterEach(async () => {
    console.log('🧹 Cleaning up user service test...')
    await testDb.resetData()
  })

  describe('getOrCreateUser', () => {
    it('should create new user when user does not exist', async () => {
      console.log('🔍 Testing getOrCreateUser for new user...')
      
      const clerkUserId = 'new-clerk-user-123'
      const userData = {
        email: '<EMAIL>',
        name: 'New User',
        avatar: 'https://example.com/avatar.jpg',
      }

      const user = await getOrCreateUser(clerkUserId, userData)

      expect(user.id).toBe(clerkUserId)
      expect(user.email).toBe(userData.email)
      expect(user.name).toBe(userData.name)
      expect(user.avatar).toBe(userData.avatar)
      expect(user.plan.name).toBe('reply-guy') // Default plan
      expect(Array.isArray(user.plan.features)).toBe(true)
      expect(user.plan.features.length).toBeGreaterThan(0)
      
      console.log('✅ New user created successfully with default plan')
    })

    it('should return existing user when user already exists', async () => {
      console.log('🔍 Testing getOrCreateUser for existing user...')
      
      // Create user first
      const existingUser = await createTestUserInDb('reply-guy', {
        id: 'existing-clerk-user-456',
        email: '<EMAIL>',
        name: 'Existing User',
      })

      const user = await getOrCreateUser(existingUser.id)

      expect(user.id).toBe(existingUser.id)
      expect(user.email).toBe(existingUser.email)
      expect(user.name).toBe(existingUser.name)
      
      console.log('✅ Existing user returned successfully')
    })

    it('should update lastActiveAt when getting existing user', async () => {
      console.log('🔍 Testing lastActiveAt update for existing user...')
      
      const existingUser = await createTestUserInDb('reply-guy', {
        id: 'active-user-789',
        email: '<EMAIL>',
        name: 'Active User',
      })

      const beforeTime = new Date()
      await new Promise(resolve => setTimeout(resolve, 10)) // Small delay
      
      const user = await getOrCreateUser(existingUser.id)
      
      expect(user.lastActiveAt).toBeDefined()
      expect(user.lastActiveAt!.getTime()).toBeGreaterThan(beforeTime.getTime())
      
      console.log('✅ lastActiveAt updated correctly')
    })

    it('should handle missing userData gracefully', async () => {
      console.log('🔍 Testing getOrCreateUser without userData...')
      
      const clerkUserId = 'minimal-user-101'
      const user = await getOrCreateUser(clerkUserId)

      expect(user.id).toBe(clerkUserId)
      expect(user.email).toBeNull()
      expect(user.name).toBeNull()
      expect(user.avatar).toBeNull()
      expect(user.plan.name).toBe('reply-guy')
      
      console.log('✅ User created with minimal data')
    })

    it('should throw error when default plan is not found', async () => {
      console.log('🔍 Testing getOrCreateUser without default plan...')
      
      // Remove all plans to simulate missing default plan
      await testDb.getClient().planFeature.deleteMany()
      await testDb.getClient().subscriptionPlan.deleteMany()

      try {
        await getOrCreateUser('no-plan-user-202')
        throw new Error('Should have thrown error for missing default plan')
      } catch (error: any) {
        expect(error.message).toContain('Default subscription plan not found')
        console.log('✅ Correctly threw error for missing default plan')
      }
    })
  })

  describe('getUserById', () => {
    it('should return user when user exists', async () => {
      console.log('🔍 Testing getUserById for existing user...')
      
      const testUser = await createTestUserInDb('reply-guy', {
        id: 'get-user-test-123',
        email: '<EMAIL>',
        name: 'Get User Test',
      })

      const user = await getUserById(testUser.id)

      expect(user).toBeDefined()
      expect(user!.id).toBe(testUser.id)
      expect(user!.email).toBe(testUser.email)
      expect(user!.plan.name).toBe('reply-guy')
      
      console.log('✅ User retrieved successfully')
    })

    it('should return null when user does not exist', async () => {
      console.log('🔍 Testing getUserById for non-existent user...')
      
      const user = await getUserById('non-existent-user-456')

      expect(user).toBeNull()
      
      console.log('✅ Correctly returned null for non-existent user')
    })
  })

  describe('updateUser', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = await createTestUserInDb('reply-guy', {
        id: 'update-user-test-789',
        email: '<EMAIL>',
        name: 'Update User Test',
      })
    })

    it('should update user information', async () => {
      console.log('🔍 Testing updateUser...')
      
      const updateData = {
        email: '<EMAIL>',
        name: 'Updated User Name',
        avatar: 'https://example.com/new-avatar.jpg',
      }

      const updatedUser = await updateUser(testUser.id, updateData)

      expect(updatedUser.email).toBe(updateData.email)
      expect(updatedUser.name).toBe(updateData.name)
      expect(updatedUser.avatar).toBe(updateData.avatar)
      expect(updatedUser.lastActiveAt).toBeDefined()
      
      console.log('✅ User updated successfully')
    })

    it('should update lastActiveAt when updating user', async () => {
      console.log('🔍 Testing lastActiveAt update on user update...')
      
      const beforeTime = new Date()
      await new Promise(resolve => setTimeout(resolve, 10))
      
      const updatedUser = await updateUser(testUser.id, { name: 'New Name' })
      
      expect(updatedUser.lastActiveAt).toBeDefined()
      expect(updatedUser.lastActiveAt!.getTime()).toBeGreaterThan(beforeTime.getTime())
      
      console.log('✅ lastActiveAt updated on user update')
    })

    it('should handle partial updates', async () => {
      console.log('🔍 Testing partial user update...')
      
      const originalEmail = testUser.email
      const updatedUser = await updateUser(testUser.id, { name: 'Only Name Updated' })

      expect(updatedUser.name).toBe('Only Name Updated')
      expect(updatedUser.email).toBe(originalEmail) // Should remain unchanged
      
      console.log('✅ Partial update handled correctly')
    })
  })

  describe('getUserUsage', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = await createTestUserInDb('reply-guy', {
        id: 'usage-test-user-101',
        email: '<EMAIL>',
        name: 'Usage Test User',
      })

      // Create some usage logs
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 10 })
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 5 })
      await createTestUsageLogInDb(testUser.id, { feature: 'IMAGE_GENERATIONS', amount: 3 })
    })

    it('should return correct usage for feature', async () => {
      console.log('🔍 Testing getUserUsage...')
      
      const aiCallsUsage = await getUserUsage(testUser.id, 'AI_CALLS')
      const imageGenUsage = await getUserUsage(testUser.id, 'IMAGE_GENERATIONS')
      const noUsageFeature = await getUserUsage(testUser.id, 'MONITORED_ACCOUNTS')

      expect(aiCallsUsage).toBe(15) // 10 + 5
      expect(imageGenUsage).toBe(3)
      expect(noUsageFeature).toBe(0)
      
      console.log('✅ Usage calculations are correct')
    })

    it('should return zero for user with no usage', async () => {
      console.log('🔍 Testing getUserUsage for user with no usage...')
      
      const newUser = await createTestUserInDb('reply-guy', {
        id: 'no-usage-user-202',
        email: '<EMAIL>',
        name: 'No Usage User',
      })

      const usage = await getUserUsage(newUser.id, 'AI_CALLS')
      expect(usage).toBe(0)
      
      console.log('✅ Zero usage returned for new user')
    })

    it('should only count current billing period', async () => {
      console.log('🔍 Testing getUserUsage billing period filtering...')
      
      // Create usage for different billing period
      const lastMonth = new Date()
      lastMonth.setMonth(lastMonth.getMonth() - 1)
      const lastMonthPeriod = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`
      
      await createTestUsageLogInDb(testUser.id, { 
        feature: 'AI_CALLS', 
        amount: 100,
        billingPeriod: lastMonthPeriod 
      })

      const currentUsage = await getUserUsage(testUser.id, 'AI_CALLS')
      
      // Should only count current month usage (15), not last month (100)
      expect(currentUsage).toBe(15)
      
      console.log('✅ Usage correctly filtered by billing period')
    })
  })

  describe('canUserUseFeature', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = await createTestUserInDb('reply-guy', {
        id: 'feature-test-user-303',
        email: '<EMAIL>',
        name: 'Feature Test User',
      })
    })

    it('should return true when user is within limits', async () => {
      console.log('🔍 Testing canUserUseFeature within limits...')

      // Create usage that's within limits (50 out of 100)
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 50 })

      const result = await canUserUseFeature(testUser.id, 'AI_CALLS')

      expect(result.allowed).toBe(true)
      expect(result.currentUsage).toBe(50)
      expect(result.limit).toBe(100)
      expect(result.resetDate).toBeDefined()

      console.log('✅ Feature usage allowed within limits')
    })

    it('should return false when user exceeds limits', async () => {
      console.log('🔍 Testing canUserUseFeature exceeding limits...')

      // Create usage that exceeds limits (150 out of 100)
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 150 })

      const result = await canUserUseFeature(testUser.id, 'AI_CALLS')

      expect(result.allowed).toBe(false)
      expect(result.currentUsage).toBe(150)
      expect(result.limit).toBe(100)

      console.log('✅ Feature usage correctly blocked when exceeding limits')
    })

    it('should handle unlimited features', async () => {
      console.log('🔍 Testing canUserUseFeature for unlimited feature...')

      // Create team plan user with unlimited team members
      const teamUser = await createTestUserInDb('team-plan', {
        id: 'team-user-404',
        email: '<EMAIL>',
        name: 'Team User',
      })

      const result = await canUserUseFeature(teamUser.id, 'TEAM_MEMBERS')

      expect(result.allowed).toBe(true)
      expect(result.limit).toBe(-1) // Unlimited

      console.log('✅ Unlimited feature handled correctly')
    })

    it('should throw error for non-existent user', async () => {
      console.log('🔍 Testing canUserUseFeature for non-existent user...')

      try {
        await canUserUseFeature('non-existent-user-505', 'AI_CALLS')
        throw new Error('Should have thrown error for non-existent user')
      } catch (error: any) {
        expect(error.message).toContain('User not found')
        console.log('✅ Correctly threw error for non-existent user')
      }
    })

    it('should throw error for unconfigured feature', async () => {
      console.log('🔍 Testing canUserUseFeature for unconfigured feature...')

      // Remove all features from user's plan
      await testDb.getClient().planFeature.deleteMany({
        where: { planId: testUser.planId }
      })

      try {
        await canUserUseFeature(testUser.id, 'AI_CALLS')
        throw new Error('Should have thrown error for unconfigured feature')
      } catch (error: any) {
        expect(error.message).toContain('Feature AI_CALLS not configured')
        console.log('✅ Correctly threw error for unconfigured feature')
      }
    })

    it('should calculate reset date correctly', async () => {
      console.log('🔍 Testing canUserUseFeature reset date calculation...')

      const result = await canUserUseFeature(testUser.id, 'AI_CALLS')

      expect(result.resetDate).toBeDefined()
      expect(result.resetDate).toBeInstanceOf(Date)

      // Reset date should be in the future (next month)
      const now = new Date()
      expect((result as any).resetDate.getTime()).toBeGreaterThan(now.getTime())

      console.log('✅ Reset date calculated correctly')
    })
  })

  describe('logUsage', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = await createTestUserInDb('reply-guy', {
        id: 'log-usage-user-606',
        email: '<EMAIL>',
        name: 'Log Usage User',
      })
    })

    it('should log usage successfully', async () => {
      console.log('🔍 Testing logUsage...')

      const metadata = { source: 'test', requestId: 'req-123' }
      const usageLog = await logUsage(testUser.id, 'AI_CALLS', 5, metadata)

      expect(usageLog.userId).toBe(testUser.id)
      expect(usageLog.feature).toBe('AI_CALLS')
      expect(usageLog.amount).toBe(5)
      expect(usageLog.metadata).toEqual(metadata)
      expect(usageLog.billingPeriod).toBeDefined()

      console.log('✅ Usage logged successfully')
    })

    it('should default amount to 1 when not provided', async () => {
      console.log('🔍 Testing logUsage with default amount...')

      const usageLog = await logUsage(testUser.id, 'IMAGE_GENERATIONS')

      expect(usageLog.amount).toBe(1)

      console.log('✅ Default amount applied correctly')
    })

    it('should set correct billing period', async () => {
      console.log('🔍 Testing logUsage billing period...')

      const usageLog = await logUsage(testUser.id, 'MONITORED_ACCOUNTS', 2)

      const now = new Date()
      const expectedPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`

      expect(usageLog.billingPeriod).toBe(expectedPeriod)

      console.log(`✅ Billing period set correctly: ${expectedPeriod}`)
    })

    it('should handle metadata correctly', async () => {
      console.log('🔍 Testing logUsage with complex metadata...')

      const complexMetadata = {
        source: 'api',
        endpoint: '/benji/generateResponse',
        model: 'gemini25Flash',
        tokensUsed: 150,
        processingTime: 2500,
        userAgent: 'BuddyChip/1.0',
      }

      const usageLog = await logUsage(testUser.id, 'AI_CALLS', 1, complexMetadata)

      expect(usageLog.metadata).toEqual(complexMetadata)

      console.log('✅ Complex metadata stored correctly')
    })

    it('should handle null metadata', async () => {
      console.log('🔍 Testing logUsage with null metadata...')

      const usageLog = await logUsage(testUser.id, 'STORAGE_GB', 1, null)

      expect(usageLog.metadata).toBeNull()

      console.log('✅ Null metadata handled correctly')
    })
  })
})
