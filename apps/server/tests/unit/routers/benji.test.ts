/**
 * Unit tests for Benji Router
 * 
 * Tests all Benji AI agent-related tRPC procedures including
 * AI response generation, usage statistics, and capabilities
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createAuthenticatedTestCaller, createUnauthenticatedTestCaller } from '../../helpers/trpc-test-client'
import { trpcTestUtils, testValidators, mockExternalServices } from '../../helpers/trpc-test-client'
import { createTestUserInDb, createTestMentionInDb, createTestUsageLogInDb } from '../../helpers/test-data'
import { testDb } from '../../helpers/db-helpers'
import type { MockUser } from '../../helpers/mock-auth'

// Mock external AI services
vi.mock('@/lib/benji-agent', () => ({
  BenjiAgent: vi.fn().mockImplementation(() => ({
    generateResponse: vi.fn().mockResolvedValue(mockExternalServices.mockAIResponse.success),
  })),
  getBenjiForUser: vi.fn().mockResolvedValue({
    generateResponse: vi.fn().mockResolvedValue(mockExternalServices.mockAIResponse.success),
  }),
}))

vi.mock('@/lib/db-utils', () => ({
  checkRateLimit: vi.fn().mockResolvedValue(mockExternalServices.mockRateLimit.allowed),
  recordUsage: vi.fn().mockResolvedValue(undefined),
  prisma: require('../../helpers/db-helpers').testPrisma,
}))

describe('Benji Router', () => {
  let testUser: any
  let mockUser: MockUser
  let authenticatedCaller: ReturnType<typeof createAuthenticatedTestCaller>
  let unauthenticatedCaller: ReturnType<typeof createUnauthenticatedTestCaller>

  beforeEach(async () => {
    console.log('🧪 Setting up Benji router test...')
    
    // Create test user in database
    testUser = await createTestUserInDb('reply-guy', {
      id: 'test-user-benji-123',
      email: '<EMAIL>',
      name: 'Benji Test User',
    })

    // Create mock user for authentication
    mockUser = {
      id: testUser.id,
      email: testUser.email!,
      name: testUser.name!,
    }

    // Create test callers
    authenticatedCaller = createAuthenticatedTestCaller(mockUser)
    unauthenticatedCaller = createUnauthenticatedTestCaller()
    
    console.log(`✅ Test user created: ${testUser.id}`)
  })

  afterEach(async () => {
    console.log('🧹 Cleaning up Benji router test...')
    await testDb.resetData()
    vi.clearAllMocks()
  })

  describe('generateMentionResponse', () => {
    let testMention: any

    beforeEach(async () => {
      // Create a test mention
      testMention = await createTestMentionInDb({
        id: 'test-mention-123',
        content: 'This is a test mention about crypto and AI',
        authorName: 'Test Author',
        authorHandle: 'testauthor',
      })
    })

    it('should generate AI response for valid mention', async () => {
      console.log('🔍 Testing generateMentionResponse for valid mention...')
      
      const response = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
          authorInfo: {
            name: testMention.authorName,
            handle: testMention.authorHandle,
          },
        }),
        (response) => {
          testValidators.validateAIResponse(response)
          expect((response as any).content).toBeDefined()
          expect((response as any).model).toBeDefined()
          expect((response as any).tokensUsed).toBeDefined()
          console.log('✅ AI response generated successfully')
          return true
        }
      )
    })

    it('should throw UNAUTHORIZED for unauthenticated user', async () => {
      console.log('🔍 Testing generateMentionResponse for unauthenticated user...')
      
      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
        })
      )
    })

    it('should validate required input fields', async () => {
      console.log('🔍 Testing generateMentionResponse input validation...')
      
      // Test missing mentionId
      await trpcTestUtils.expectTRPCError(
        // @ts-expect-error - Testing missing required field
        authenticatedCaller.benji.generateMentionResponse({
          mentionContent: testMention.content,
        }),
        'BAD_REQUEST'
      )

      // Test missing mentionContent
      await trpcTestUtils.expectTRPCError(
        // @ts-expect-error - Testing missing required field
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
        }),
        'BAD_REQUEST'
      )
    })

    it('should handle rate limit exceeded', async () => {
      console.log('🔍 Testing generateMentionResponse rate limit...')
      
      // Mock rate limit exceeded
      const { checkRateLimit } = require('@/lib/db-utils')
      checkRateLimit.mockResolvedValueOnce(mockExternalServices.mockRateLimit.exceeded)
      
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
        }),
        'TOO_MANY_REQUESTS'
      )
    })

    it('should handle AI service errors gracefully', async () => {
      console.log('🔍 Testing generateMentionResponse AI service error...')
      
      // Mock AI service failure
      const { getBenjiForUser } = require('@/lib/benji-agent')
      getBenjiForUser.mockRejectedValueOnce(mockExternalServices.mockAIResponse.error)
      
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
        }),
        'INTERNAL_SERVER_ERROR'
      )
    })

    it('should handle empty mention content', async () => {
      console.log('🔍 Testing generateMentionResponse with empty content...')
      
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: '',
        }),
        'BAD_REQUEST'
      )
    })

    it('should include author info when provided', async () => {
      console.log('🔍 Testing generateMentionResponse with author info...')
      
      const authorInfo = {
        name: 'Test Author Name',
        handle: 'testhandle',
        avatarUrl: 'https://example.com/avatar.jpg',
      }
      
      const response = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
          authorInfo,
        }),
        (response) => {
          expect((response as any).content).toBeDefined()
          console.log('✅ Response generated with author info')
          return true
        }
      )
    })
  })

  describe('generateQuickReply', () => {
    it('should generate quick reply for valid tweet', async () => {
      console.log('🔍 Testing generateQuickReply for valid tweet...')
      
      const response = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/testuser/status/123456789',
          tweetContent: 'This is an interesting tweet about technology',
        }),
        (response) => {
          testValidators.validateAIResponse(response)
          expect((response as any).content).toBeDefined()
          console.log('✅ Quick reply generated successfully')
          return true
        }
      )
    })

    it('should throw UNAUTHORIZED for unauthenticated user', async () => {
      console.log('🔍 Testing generateQuickReply for unauthenticated user...')
      
      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/testuser/status/123456789',
          tweetContent: 'Test tweet content',
        })
      )
    })

    it('should validate tweet URL format', async () => {
      console.log('🔍 Testing generateQuickReply URL validation...')
      
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'invalid-url',
          tweetContent: 'Test tweet content',
        }),
        'BAD_REQUEST'
      )
    })

    it('should validate required fields', async () => {
      console.log('🔍 Testing generateQuickReply required fields...')
      
      // Test missing tweetUrl
      await trpcTestUtils.expectTRPCError(
        // @ts-expect-error - Testing missing required field
        authenticatedCaller.benji.generateQuickReply({
          tweetContent: 'Test content',
        }),
        'BAD_REQUEST'
      )

      // Test missing tweetContent
      await trpcTestUtils.expectTRPCError(
        // @ts-expect-error - Testing missing required field
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/test/status/123',
        }),
        'BAD_REQUEST'
      )
    })

    it('should handle rate limit exceeded', async () => {
      console.log('🔍 Testing generateQuickReply rate limit...')
      
      // Mock rate limit exceeded
      const { checkRateLimit } = require('@/lib/db-utils')
      checkRateLimit.mockResolvedValueOnce(mockExternalServices.mockRateLimit.exceeded)
      
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/testuser/status/123456789',
          tweetContent: 'Test tweet content',
        }),
        'TOO_MANY_REQUESTS'
      )
    })
  })

  describe('getUsageStats', () => {
    beforeEach(async () => {
      // Create some usage logs for testing
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 10 })
      await createTestUsageLogInDb(testUser.id, { feature: 'IMAGE_GENERATIONS', amount: 3 })
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 5 })
    })

    it('should return usage statistics for authenticated user', async () => {
      console.log('🔍 Testing getUsageStats for authenticated user...')

      const stats = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.getUsageStats(),
        (stats) => {
          expect((stats as any).currentPeriod).toBeDefined()
          expect((stats as any).usage).toBeDefined()
          expect(typeof (stats as any).usage).toBe('object')

          // Check specific usage values
          expect(stats.usage.AI_CALLS).toBe(15) // 10 + 5
          expect(stats.usage.IMAGE_GENERATIONS).toBe(3)

          console.log('✅ Usage statistics structure and values are correct')
          return true
        }
      )

      // Verify current period format
      const now = new Date()
      const expectedPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      expect(stats.currentPeriod).toBe(expectedPeriod)
    })

    it('should throw UNAUTHORIZED for unauthenticated user', async () => {
      console.log('🔍 Testing getUsageStats for unauthenticated user...')

      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.getUsageStats()
      )
    })

    it('should return empty usage for user with no usage history', async () => {
      console.log('🔍 Testing getUsageStats for user with no usage...')

      // Create a new user with no usage
      const newUser = await createTestUserInDb('reply-guy', {
        id: 'new-user-benji-456',
        email: '<EMAIL>',
        name: 'New Benji User',
      })

      const newMockUser: MockUser = {
        id: newUser.id,
        email: newUser.email!,
        name: newUser.name!,
      }

      const newUserCaller = createAuthenticatedTestCaller(newMockUser)

      const stats = await trpcTestUtils.expectSuccess(
        newUserCaller.benji.getUsageStats(),
        (stats) => {
          expect((stats as any).usage).toEqual({})
          console.log('✅ Empty usage correctly returned for new user')
          return true
        }
      )
    })

    it('should group usage by feature correctly', async () => {
      console.log('🔍 Testing getUsageStats feature grouping...')

      const stats = await authenticatedCaller.benji.getUsageStats()

      // Verify that usage is properly grouped by feature
      expect((stats as any).usage).toHaveProperty('AI_CALLS')
      expect((stats as any).usage).toHaveProperty('IMAGE_GENERATIONS')
      expect((stats as any).usage.AI_CALLS).toBe(15) // Sum of all AI_CALLS usage
      expect((stats as any).usage.IMAGE_GENERATIONS).toBe(3)

      console.log('✅ Usage properly grouped by feature')
    })
  })

  describe('getCapabilities', () => {
    it('should return capabilities for authenticated user', async () => {
      console.log('🔍 Testing getCapabilities for authenticated user...')

      const capabilities = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.getCapabilities(),
        (capabilities) => {
          testValidators.validateCapabilities(capabilities)
          expect(capabilities.plan).toBe('reply-guy')
          expect(Array.isArray(capabilities.models)).toBe(true)
          expect(Array.isArray(capabilities.tools)).toBe(true)
          console.log('✅ Capabilities structure is valid')
          return true
        }
      )

      // Check specific models for reply-guy plan
      const geminiFlash = capabilities.models.find(m => m.id === 'gemini25Flash')
      const geminiPro = capabilities.models.find(m => m.id === 'gemini25Pro')
      const openaiO3 = capabilities.models.find(m => m.id === 'openaiO3')

      expect(geminiFlash?.available).toBe(true)
      expect(geminiPro?.available).toBe(false) // Not available for reply-guy plan
      expect(openaiO3?.available).toBe(false) // Only for team plan

      // Check tools
      const webSearch = capabilities.tools.find(t => t.name === 'Web Search')
      const imageGen = capabilities.tools.find(t => t.name === 'Image Generation')

      expect(webSearch?.enabled).toBe(true)
      expect(imageGen?.enabled).toBe(false) // Not available for reply-guy plan
    })

    it('should throw UNAUTHORIZED for unauthenticated user', async () => {
      console.log('🔍 Testing getCapabilities for unauthenticated user...')

      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.getCapabilities()
      )
    })

    it('should return different capabilities for different plans', async () => {
      console.log('🔍 Testing getCapabilities for different plans...')

      // Create user with reply-god plan
      const replyGodUser = await createTestUserInDb('reply-god', {
        id: 'reply-god-user-789',
        email: '<EMAIL>',
        name: 'Reply God User',
      })

      const replyGodMockUser: MockUser = {
        id: replyGodUser.id,
        email: replyGodUser.email!,
        name: replyGodUser.name!,
      }

      const replyGodCaller = createAuthenticatedTestCaller(replyGodMockUser)

      const capabilities = await trpcTestUtils.expectSuccess(
        replyGodCaller.benji.getCapabilities(),
        (capabilities) => {
          expect(capabilities.plan).toBe('reply-god')
          console.log('✅ Reply God capabilities returned')
          return true
        }
      )

      // Check that reply-god has more capabilities
      const geminiPro = capabilities.models.find(m => m.id === 'gemini25Pro')
      const imageGen = capabilities.tools.find(t => t.name === 'Image Generation')

      expect(geminiPro?.available).toBe(true) // Available for reply-god
      expect(imageGen?.enabled).toBe(true) // Available for reply-god
    })

    it('should handle user not found', async () => {
      console.log('🔍 Testing getCapabilities for non-existent user...')

      const nonExistentUser: MockUser = {
        id: 'non-existent-benji-user',
        email: '<EMAIL>',
        name: 'Non Existent User',
      }

      const callerWithNonExistentUser = createAuthenticatedTestCaller(nonExistentUser)

      await trpcTestUtils.expectTRPCError(
        callerWithNonExistentUser.benji.getCapabilities(),
        'NOT_FOUND'
      )
    })

    it('should include all expected model information', async () => {
      console.log('🔍 Testing getCapabilities model information...')

      const capabilities = await authenticatedCaller.benji.getCapabilities()

      capabilities.models.forEach(model => {
        expect(model).toHaveProperty('id')
        expect(model).toHaveProperty('name')
        expect(model).toHaveProperty('available')
        expect(model).toHaveProperty('description')
        expect(typeof model.available).toBe('boolean')
        console.log(`✅ Model ${model.id} has complete information`)
      })
    })

    it('should include all expected tool information', async () => {
      console.log('🔍 Testing getCapabilities tool information...')

      const capabilities = await authenticatedCaller.benji.getCapabilities()

      capabilities.tools.forEach(tool => {
        expect(tool).toHaveProperty('name')
        expect(tool).toHaveProperty('description')
        expect(tool).toHaveProperty('enabled')
        expect(typeof tool.enabled).toBe('boolean')
        console.log(`✅ Tool ${tool.name} has complete information`)
      })
    })
  })
})
