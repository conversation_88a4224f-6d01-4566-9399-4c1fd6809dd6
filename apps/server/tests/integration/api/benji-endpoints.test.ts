/**
 * Integration tests for Benji API endpoints
 * 
 * Tests full request/response cycles for AI agent endpoints
 * including rate limiting, AI service integration, and database operations
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createAuthenticatedTestCaller, createUnauthenticatedTestCaller } from '../../helpers/trpc-test-client'
import { trpcTestUtils, testValidators, mockExternalServices } from '../../helpers/trpc-test-client'
import { createTestUserInDb, createTestMentionInDb, createTestUsageLogInDb } from '../../helpers/test-data'
import { testDb, seedTestData } from '../../helpers/db-helpers'
import { mockAuthenticatedUser, mockUnauthenticatedUser } from '../../helpers/mock-auth'
import type { MockUser } from '../../helpers/mock-auth'

// Mock external services
vi.mock('@/lib/benji-agent', () => ({
  BenjiAgent: vi.fn().mockImplementation(() => ({
    generateResponse: vi.fn().mockResolvedValue(mockExternalServices.mockAIResponse.success),
  })),
  getBenjiForUser: vi.fn().mockResolvedValue({
    generateResponse: vi.fn().mockResolvedValue(mockExternalServices.mockAIResponse.success),
  }),
}))

vi.mock('@/lib/db-utils', () => ({
  checkRateLimit: vi.fn().mockResolvedValue(mockExternalServices.mockRateLimit.allowed),
  recordUsage: vi.fn().mockResolvedValue(undefined),
  prisma: require('../../helpers/db-helpers').testPrisma,
}))

describe('Benji API Endpoints Integration', () => {
  let testUser: any
  let mockUser: MockUser
  let authenticatedCaller: ReturnType<typeof createAuthenticatedTestCaller>
  let unauthenticatedCaller: ReturnType<typeof createUnauthenticatedTestCaller>

  beforeEach(async () => {
    console.log('🧪 Setting up Benji endpoints integration test...')
    
    // Seed test data
    await seedTestData()
    
    // Create test user in database
    testUser = await createTestUserInDb('reply-guy', {
      id: 'benji-integration-user-123',
      email: '<EMAIL>',
      name: 'Benji Integration Test User',
    })

    // Create mock user for authentication
    mockUser = {
      id: testUser.id,
      email: testUser.email!,
      name: testUser.name!,
    }

    // Setup authentication
    mockAuthenticatedUser(mockUser)

    // Create test callers
    authenticatedCaller = createAuthenticatedTestCaller(mockUser)
    unauthenticatedCaller = createUnauthenticatedTestCaller()
    
    console.log(`✅ Benji integration test setup complete: ${testUser.id}`)
  })

  afterEach(async () => {
    console.log('🧹 Cleaning up Benji endpoints integration test...')
    mockUnauthenticatedUser()
    await testDb.resetData()
    vi.clearAllMocks()
  })

  describe('AI Response Generation Flow', () => {
    let testMention: any

    beforeEach(async () => {
      testMention = await createTestMentionInDb({
        id: 'integration-mention-123',
        content: 'This is a test mention about crypto and blockchain technology',
        authorName: 'Test Author',
        authorHandle: 'testauthor',
      })
    })

    it('should handle complete mention response generation flow', async () => {
      console.log('🔍 Testing complete mention response generation flow...')
      
      // Step 1: Generate AI response
      const response = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
          authorInfo: {
            name: testMention.authorName,
            handle: testMention.authorHandle,
          },
        }),
        (response) => {
          testValidators.validateAIResponse(response)
          expect((response as any).content).toBeDefined()
          expect((response as any).model).toBeDefined()
          console.log('✅ AI response generated successfully')
          return true
        }
      )

      // Step 2: Verify rate limiting was checked
      const { checkRateLimit } = require('@/lib/db-utils')
      expect(checkRateLimit).toHaveBeenCalledWith(testUser.id, 'AI_CALLS', 1)

      // Step 3: Verify usage was recorded
      const { recordUsage } = require('@/lib/db-utils')
      expect(recordUsage).toHaveBeenCalledWith(testUser.id, 'AI_CALLS', 1, expect.any(Object))

      // Step 4: Verify AI service was called
      const { getBenjiForUser } = require('@/lib/benji-agent')
      expect(getBenjiForUser).toHaveBeenCalledWith(testUser.id)

      console.log('✅ Complete mention response generation flow successful')
    })

    it('should handle rate limiting in generation flow', async () => {
      console.log('🔍 Testing rate limiting in generation flow...')
      
      // Mock rate limit exceeded
      const { checkRateLimit } = require('@/lib/db-utils')
      checkRateLimit.mockResolvedValueOnce(mockExternalServices.mockRateLimit.exceeded)

      // Attempt to generate response
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
        }),
        'TOO_MANY_REQUESTS'
      )

      // Verify that usage was not recorded when rate limited
      const { recordUsage } = require('@/lib/db-utils')
      expect(recordUsage).not.toHaveBeenCalled()

      console.log('✅ Rate limiting in generation flow handled correctly')
    })

    it('should handle AI service failures gracefully', async () => {
      console.log('🔍 Testing AI service failure handling...')
      
      // Mock AI service failure
      const { getBenjiForUser } = require('@/lib/benji-agent')
      getBenjiForUser.mockRejectedValueOnce(mockExternalServices.mockAIResponse.error)

      // Attempt to generate response
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateMentionResponse({
          mentionId: testMention.id,
          mentionContent: testMention.content,
        }),
        'INTERNAL_SERVER_ERROR'
      )

      console.log('✅ AI service failure handled gracefully')
    })
  })

  describe('Quick Reply Generation Flow', () => {
    it('should handle complete quick reply generation flow', async () => {
      console.log('🔍 Testing complete quick reply generation flow...')
      
      const tweetUrl = 'https://twitter.com/testuser/status/123456789'
      const tweetContent = 'This is an interesting tweet about AI and technology'

      // Step 1: Generate quick reply
      const response = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl,
          tweetContent,
        }),
        (response) => {
          testValidators.validateAIResponse(response)
          expect((response as any).content).toBeDefined()
          console.log('✅ Quick reply generated successfully')
          return true
        }
      )

      // Step 2: Verify rate limiting was checked
      const { checkRateLimit } = require('@/lib/db-utils')
      expect(checkRateLimit).toHaveBeenCalledWith(testUser.id, 'AI_CALLS', 1)

      // Step 3: Verify usage was recorded
      const { recordUsage } = require('@/lib/db-utils')
      expect(recordUsage).toHaveBeenCalledWith(testUser.id, 'AI_CALLS', 1, expect.any(Object))

      console.log('✅ Complete quick reply generation flow successful')
    })

    it('should validate tweet URL format', async () => {
      console.log('🔍 Testing tweet URL validation...')
      
      await trpcTestUtils.expectTRPCError(
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'invalid-url-format',
          tweetContent: 'Test content',
        }),
        'BAD_REQUEST'
      )

      console.log('✅ Tweet URL validation working correctly')
    })
  })

  describe('Usage Statistics Integration', () => {
    beforeEach(async () => {
      // Create usage logs for testing
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 15 })
      await createTestUsageLogInDb(testUser.id, { feature: 'IMAGE_GENERATIONS', amount: 8 })
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 12 })
    })

    it('should handle complete usage statistics flow', async () => {
      console.log('🔍 Testing complete usage statistics flow...')
      
      // Step 1: Get usage statistics
      const stats = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.getUsageStats(),
        (stats) => {
          expect((stats as any).currentPeriod).toBeDefined()
          expect((stats as any).usage).toBeDefined()
          expect((stats as any).usage.AI_CALLS).toBe(27) // 15 + 12
          expect((stats as any).usage.IMAGE_GENERATIONS).toBe(8)
          console.log('✅ Usage statistics retrieved successfully')
          return true
        }
      )

      // Step 2: Verify current period format
      const now = new Date()
      const expectedPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      expect(stats.currentPeriod).toBe(expectedPeriod)

      // Step 3: Generate new usage and verify it's reflected
      await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/test/status/456',
          tweetContent: 'Test tweet for usage tracking',
        }),
        () => {
          console.log('✅ New AI call generated')
          return true
        }
      )

      // Note: In a real integration test, we would verify the usage was updated
      // but since we're mocking the recordUsage function, we just verify it was called
      const { recordUsage } = require('@/lib/db-utils')
      expect(recordUsage).toHaveBeenCalled()

      console.log('✅ Complete usage statistics flow successful')
    })

    it('should handle empty usage history', async () => {
      console.log('🔍 Testing usage statistics with empty history...')
      
      // Create new user with no usage
      const newUser = await createTestUserInDb('reply-guy', {
        id: 'empty-usage-user-456',
        email: '<EMAIL>',
        name: 'Empty Usage User',
      })

      const newMockUser: MockUser = {
        id: newUser.id,
        email: newUser.email!,
        name: newUser.name!,
      }

      mockAuthenticatedUser(newMockUser)
      const newUserCaller = createAuthenticatedTestCaller(newMockUser)

      const stats = await trpcTestUtils.expectSuccess(
        newUserCaller.benji.getUsageStats(),
        (stats) => {
          expect(stats.usage).toEqual({})
          console.log('✅ Empty usage history handled correctly')
          return true
        }
      )

      console.log('✅ Empty usage history test successful')
    })
  })

  describe('Capabilities Integration', () => {
    it('should handle complete capabilities flow', async () => {
      console.log('🔍 Testing complete capabilities flow...')
      
      // Step 1: Get capabilities for reply-guy plan
      const capabilities = await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.getCapabilities(),
        (capabilities) => {
          testValidators.validateCapabilities(capabilities)
          expect(capabilities.plan).toBe('reply-guy')
          console.log('✅ Capabilities retrieved successfully')
          return true
        }
      )

      // Step 2: Verify plan-specific model availability
      const geminiFlash = capabilities.models.find(m => m.id === 'gemini25Flash')
      const geminiPro = capabilities.models.find(m => m.id === 'gemini25Pro')
      const openaiO3 = capabilities.models.find(m => m.id === 'openaiO3')

      expect(geminiFlash?.available).toBe(true)
      expect(geminiPro?.available).toBe(false) // Not available for reply-guy
      expect(openaiO3?.available).toBe(false) // Only for team plan

      // Step 3: Verify plan-specific tool availability
      const webSearch = capabilities.tools.find(t => t.name === 'Web Search')
      const imageGen = capabilities.tools.find(t => t.name === 'Image Generation')

      expect(webSearch?.enabled).toBe(true)
      expect(imageGen?.enabled).toBe(false) // Not available for reply-guy

      console.log('✅ Complete capabilities flow successful')
    })

    it('should show different capabilities for different plans', async () => {
      console.log('🔍 Testing capabilities for different plans...')
      
      // Create reply-god user
      const replyGodUser = await createTestUserInDb('reply-god', {
        id: 'reply-god-capabilities-789',
        email: '<EMAIL>',
        name: 'Reply God Capabilities User',
      })

      const replyGodMockUser: MockUser = {
        id: replyGodUser.id,
        email: replyGodUser.email!,
        name: replyGodUser.name!,
      }

      mockAuthenticatedUser(replyGodMockUser)
      const replyGodCaller = createAuthenticatedTestCaller(replyGodMockUser)

      const capabilities = await trpcTestUtils.expectSuccess(
        replyGodCaller.benji.getCapabilities(),
        (capabilities) => {
          expect(capabilities.plan).toBe('reply-god')
          console.log('✅ Reply God capabilities retrieved')
          return true
        }
      )

      // Verify reply-god has more capabilities
      const geminiPro = capabilities.models.find(m => m.id === 'gemini25Pro')
      const imageGen = capabilities.tools.find(t => t.name === 'Image Generation')

      expect(geminiPro?.available).toBe(true) // Available for reply-god
      expect(imageGen?.enabled).toBe(true) // Available for reply-god

      console.log('✅ Different plan capabilities test successful')
    })
  })

  describe('Authentication Integration', () => {
    it('should handle authentication across all endpoints', async () => {
      console.log('🔍 Testing authentication across all Benji endpoints...')
      
      // Switch to unauthenticated state
      mockUnauthenticatedUser()

      // Test all endpoints require authentication
      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.generateMentionResponse({
          mentionId: 'test-mention',
          mentionContent: 'test content',
        })
      )

      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/test/status/123',
          tweetContent: 'test content',
        })
      )

      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.getUsageStats()
      )

      await trpcTestUtils.expectUnauthorized(
        unauthenticatedCaller.benji.getCapabilities()
      )

      console.log('✅ Authentication required for all endpoints')
    })
  })
})
