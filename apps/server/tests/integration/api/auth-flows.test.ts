/**
 * Integration tests for Authentication Flows
 * 
 * Tests authentication and authorization flows across all endpoints
 * including user creation, session management, and access control
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createAuthenticatedTestCaller, createUnauthenticatedTestCaller } from '../../helpers/trpc-test-client'
import { trpcTestUtils } from '../../helpers/trpc-test-client'
import { createTestUserInDb } from '../../helpers/test-data'
import { testDb, seedTestData } from '../../helpers/db-helpers'
import { mockAuthenticatedUser, mockUnauthenticatedUser, resetMockAuth } from '../../helpers/mock-auth'
import type { MockUser } from '../../helpers/mock-auth'

describe('Authentication Flows Integration', () => {
  beforeEach(async () => {
    console.log('🧪 Setting up authentication flows test...')
    await seedTestData()
  })

  afterEach(async () => {
    console.log('🧹 Cleaning up authentication flows test...')
    resetMockAuth()
    await testDb.resetData()
  })

  describe('User Authentication States', () => {
    it('should handle unauthenticated access correctly', async () => {
      console.log('🔍 Testing unauthenticated access...')
      
      mockUnauthenticatedUser()
      const unauthenticatedCaller = createUnauthenticatedTestCaller()

      // Health check should work without authentication
      await trpcTestUtils.expectSuccess(
        unauthenticatedCaller.healthCheck(),
        (result) => {
          expect(result).toBe('OK')
          console.log('✅ Health check accessible without auth')
          return true
        }
      )

      // Protected endpoints should require authentication
      const protectedEndpoints = [
        () => unauthenticatedCaller.user.getProfile(),
        () => unauthenticatedCaller.user.getUsage(),
        () => unauthenticatedCaller.user.canUseFeature({ feature: 'AI_CALLS' }),
        () => unauthenticatedCaller.benji.generateMentionResponse({
          mentionId: 'test',
          mentionContent: 'test',
        }),
        () => unauthenticatedCaller.benji.generateQuickReply({
          tweetUrl: 'https://twitter.com/test/status/123',
          tweetContent: 'test',
        }),
        () => unauthenticatedCaller.benji.getUsageStats(),
        () => unauthenticatedCaller.benji.getCapabilities(),
      ]

      for (const endpoint of protectedEndpoints) {
        await trpcTestUtils.expectUnauthorized(endpoint())
      }

      console.log('✅ Unauthenticated access handled correctly')
    })

    it('should handle authenticated access correctly', async () => {
      console.log('🔍 Testing authenticated access...')
      
      // Create test user
      const testUser = await createTestUserInDb('reply-guy', {
        id: 'auth-test-user-123',
        email: '<EMAIL>',
        name: 'Auth Test User',
      })

      const mockUser: MockUser = {
        id: testUser.id,
        email: testUser.email!,
        name: testUser.name!,
      }

      mockAuthenticatedUser(mockUser)
      const authenticatedCaller = createAuthenticatedTestCaller(mockUser)

      // All endpoints should work with authentication
      await trpcTestUtils.expectSuccess(
        authenticatedCaller.user.getProfile(),
        (result) => {
          expect(result.user.id).toBe(testUser.id)
          console.log('✅ getProfile accessible with auth')
          return true
        }
      )

      await trpcTestUtils.expectSuccess(
        authenticatedCaller.user.getUsage(),
        (result) => {
          expect(Array.isArray(result)).toBe(true)
          console.log('✅ getUsage accessible with auth')
          return true
        }
      )

      await trpcTestUtils.expectSuccess(
        authenticatedCaller.user.canUseFeature({ feature: 'AI_CALLS' }),
        (result) => {
          expect(result).toHaveProperty('allowed')
          console.log('✅ canUseFeature accessible with auth')
          return true
        }
      )

      await trpcTestUtils.expectSuccess(
        authenticatedCaller.benji.getCapabilities(),
        (result) => {
          expect(result).toHaveProperty('plan')
          console.log('✅ getCapabilities accessible with auth')
          return true
        }
      )

      console.log('✅ Authenticated access handled correctly')
    })

    it('should handle session transitions correctly', async () => {
      console.log('🔍 Testing session transitions...')
      
      const testUser = await createTestUserInDb('reply-guy', {
        id: 'session-test-user-456',
        email: '<EMAIL>',
        name: 'Session Test User',
      })

      const mockUser: MockUser = {
        id: testUser.id,
        email: testUser.email!,
        name: testUser.name!,
      }

      // Start unauthenticated
      mockUnauthenticatedUser()
      let caller = createUnauthenticatedTestCaller()

      await trpcTestUtils.expectUnauthorized(
        caller.user.getProfile()
      )

      // Authenticate
      mockAuthenticatedUser(mockUser)
      caller = createAuthenticatedTestCaller(mockUser)

      await trpcTestUtils.expectSuccess(
        caller.user.getProfile(),
        (result) => {
          expect(result.user.id).toBe(testUser.id)
          console.log('✅ Profile accessible after authentication')
          return true
        }
      )

      // Logout
      mockUnauthenticatedUser()
      caller = createUnauthenticatedTestCaller()

      await trpcTestUtils.expectUnauthorized(
        caller.user.getProfile()
      )

      console.log('✅ Session transitions handled correctly')
    })
  })

  describe('User Context and Data Access', () => {
    it('should ensure users can only access their own data', async () => {
      console.log('🔍 Testing user data isolation...')
      
      // Create two test users
      const user1 = await createTestUserInDb('reply-guy', {
        id: 'isolation-user-1',
        email: '<EMAIL>',
        name: 'User One',
      })

      const user2 = await createTestUserInDb('reply-god', {
        id: 'isolation-user-2',
        email: '<EMAIL>',
        name: 'User Two',
      })

      // Test user 1 access
      const mockUser1: MockUser = {
        id: user1.id,
        email: user1.email!,
        name: user1.name!,
      }

      mockAuthenticatedUser(mockUser1)
      const caller1 = createAuthenticatedTestCaller(mockUser1)

      const profile1 = await trpcTestUtils.expectSuccess(
        caller1.user.getProfile(),
        (result) => {
          expect(result.user.id).toBe(user1.id)
          expect(result.plan.name).toBe('reply-guy')
          console.log('✅ User 1 can access their own data')
          return true
        }
      )

      // Test user 2 access
      const mockUser2: MockUser = {
        id: user2.id,
        email: user2.email!,
        name: user2.name!,
      }

      mockAuthenticatedUser(mockUser2)
      const caller2 = createAuthenticatedTestCaller(mockUser2)

      const profile2 = await trpcTestUtils.expectSuccess(
        caller2.user.getProfile(),
        (result) => {
          expect(result.user.id).toBe(user2.id)
          expect(result.plan.name).toBe('reply-god')
          console.log('✅ User 2 can access their own data')
          return true
        }
      )

      // Verify users get different data
      expect(profile1.user.id).not.toBe(profile2.user.id)
      expect(profile1.plan.name).not.toBe(profile2.plan.name)

      console.log('✅ User data isolation working correctly')
    })

    it('should handle non-existent user authentication', async () => {
      console.log('🔍 Testing non-existent user authentication...')
      
      const nonExistentUser: MockUser = {
        id: 'non-existent-user-789',
        email: '<EMAIL>',
        name: 'Non Existent User',
      }

      mockAuthenticatedUser(nonExistentUser)
      const caller = createAuthenticatedTestCaller(nonExistentUser)

      // Some endpoints should handle non-existent users gracefully
      await trpcTestUtils.expectTRPCError(
        caller.user.getProfile(),
        'NOT_FOUND'
      )

      await trpcTestUtils.expectTRPCError(
        caller.benji.getCapabilities(),
        'NOT_FOUND'
      )

      console.log('✅ Non-existent user authentication handled correctly')
    })
  })

  describe('Plan-Based Access Control', () => {
    it('should enforce plan-based feature access', async () => {
      console.log('🔍 Testing plan-based feature access...')
      
      // Create users with different plans
      const replyGuyUser = await createTestUserInDb('reply-guy', {
        id: 'plan-access-reply-guy',
        email: '<EMAIL>',
        name: 'Reply Guy User',
      })

      const teamUser = await createTestUserInDb('team-plan', {
        id: 'plan-access-team',
        email: '<EMAIL>',
        name: 'Team User',
      })

      // Test reply-guy capabilities
      const replyGuyMockUser: MockUser = {
        id: replyGuyUser.id,
        email: replyGuyUser.email!,
        name: replyGuyUser.name!,
      }

      mockAuthenticatedUser(replyGuyMockUser)
      const replyGuyCaller = createAuthenticatedTestCaller(replyGuyMockUser)

      const replyGuyCapabilities = await trpcTestUtils.expectSuccess(
        replyGuyCaller.benji.getCapabilities(),
        (result) => {
          expect(result.plan).toBe('reply-guy')
          console.log('✅ Reply Guy capabilities retrieved')
          return true
        }
      )

      // Test team plan capabilities
      const teamMockUser: MockUser = {
        id: teamUser.id,
        email: teamUser.email!,
        name: teamUser.name!,
      }

      mockAuthenticatedUser(teamMockUser)
      const teamCaller = createAuthenticatedTestCaller(teamMockUser)

      const teamCapabilities = await trpcTestUtils.expectSuccess(
        teamCaller.benji.getCapabilities(),
        (result) => {
          expect(result.plan).toBe('team-plan')
          console.log('✅ Team plan capabilities retrieved')
          return true
        }
      )

      // Verify different capabilities
      const replyGuyGeminiPro = replyGuyCapabilities.models.find(m => m.id === 'gemini25Pro')
      const teamGeminiPro = teamCapabilities.models.find(m => m.id === 'gemini25Pro')
      const teamOpenAI = teamCapabilities.models.find(m => m.id === 'openaiO3')

      expect(replyGuyGeminiPro?.available).toBe(false)
      expect(teamGeminiPro?.available).toBe(true)
      expect(teamOpenAI?.available).toBe(true)

      console.log('✅ Plan-based feature access working correctly')
    })

    it('should enforce plan-based usage limits', async () => {
      console.log('🔍 Testing plan-based usage limits...')
      
      const replyGuyUser = await createTestUserInDb('reply-guy', {
        id: 'limits-reply-guy',
        email: '<EMAIL>',
        name: 'Limits Reply Guy User',
      })

      const replyGodUser = await createTestUserInDb('reply-god', {
        id: 'limits-reply-god',
        email: '<EMAIL>',
        name: 'Limits Reply God User',
      })

      // Check reply-guy limits
      const replyGuyMockUser: MockUser = {
        id: replyGuyUser.id,
        email: replyGuyUser.email!,
        name: replyGuyUser.name!,
      }

      mockAuthenticatedUser(replyGuyMockUser)
      const replyGuyCaller = createAuthenticatedTestCaller(replyGuyMockUser)

      const replyGuyLimits = await trpcTestUtils.expectSuccess(
        replyGuyCaller.user.canUseFeature({ feature: 'AI_CALLS' }),
        (result) => {
          expect(result.limit).toBe(100) // Reply Guy limit
          console.log('✅ Reply Guy limits checked')
          return true
        }
      )

      // Check reply-god limits
      const replyGodMockUser: MockUser = {
        id: replyGodUser.id,
        email: replyGodUser.email!,
        name: replyGodUser.name!,
      }

      mockAuthenticatedUser(replyGodMockUser)
      const replyGodCaller = createAuthenticatedTestCaller(replyGodMockUser)

      const replyGodLimits = await trpcTestUtils.expectSuccess(
        replyGodCaller.user.canUseFeature({ feature: 'AI_CALLS' }),
        (result) => {
          expect(result.limit).toBe(500) // Reply God limit
          console.log('✅ Reply God limits checked')
          return true
        }
      )

      // Verify different limits
      expect(replyGuyLimits.limit).toBeLessThan(replyGodLimits.limit)

      console.log('✅ Plan-based usage limits working correctly')
    })
  })

  describe('Error Handling in Authentication', () => {
    it('should handle malformed authentication gracefully', async () => {
      console.log('🔍 Testing malformed authentication handling...')
      
      // Test with invalid user ID format
      const invalidUser: MockUser = {
        id: '', // Empty ID
        email: '<EMAIL>',
        name: 'Invalid User',
      }

      mockAuthenticatedUser(invalidUser)
      const caller = createAuthenticatedTestCaller(invalidUser)

      // Should handle empty user ID gracefully
      await trpcTestUtils.expectTRPCError(
        caller.user.getProfile(),
        'NOT_FOUND'
      )

      console.log('✅ Malformed authentication handled gracefully')
    })

    it('should maintain security across concurrent requests', async () => {
      console.log('🔍 Testing security across concurrent requests...')
      
      const testUser = await createTestUserInDb('reply-guy', {
        id: 'concurrent-auth-user',
        email: '<EMAIL>',
        name: 'Concurrent Auth User',
      })

      const mockUser: MockUser = {
        id: testUser.id,
        email: testUser.email!,
        name: testUser.name!,
      }

      mockAuthenticatedUser(mockUser)
      const caller = createAuthenticatedTestCaller(mockUser)

      // Make multiple concurrent authenticated requests
      const concurrentRequests = [
        caller.user.getProfile(),
        caller.user.getUsage(),
        caller.benji.getCapabilities(),
        caller.user.canUseFeature({ feature: 'AI_CALLS' }),
      ]

      const results = await Promise.all(concurrentRequests)

      // All requests should succeed and return data for the same user
      expect((results[0] as any).user.id).toBe(testUser.id);
      expect(Array.isArray(results[1])).toBe(true);
      expect((results[2] as any).plan).toBe('reply-guy');
      expect(results[3]).toHaveProperty('allowed');

      console.log('✅ Security maintained across concurrent requests')
    })
  })
})
