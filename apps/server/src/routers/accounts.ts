/**
 * Accounts Router for BuddyChip tRPC API - Fixed version matching schema
 * 
 * <PERSON>les monitored account management including:
 * - Adding/removing Twitter accounts to monitor
 * - Managing account status and settings
 * - Account statistics and monitoring
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { protectedProcedure, createTRPCRouter } from '../lib/trpc';
import { twitterClient } from '../lib/twitter-client';
import { canUserUseFeature, logUsage } from '../lib/user-service';

export const accountsRouter = createTRPCRouter({
  /**
   * Get all monitored accounts for the current user
   */
  getMonitored: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const accounts = await ctx.prisma.monitoredAccount.findMany({
          where: {
            userId: ctx.userId!,
          },
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            _count: {
              select: {
                mentions: true,
              },
            },
          },
        });

        return {
          success: true,
          accounts: accounts.map(account => ({
            id: account.id,
            handle: account.twitterHandle,
            displayName: account.displayName,
            avatar: account.avatarUrl,
            isActive: account.isActive,
            platform: 'twitter',
            followerCount: 0, // Not available in current schema
            createdAt: account.createdAt,
            lastSyncAt: account.lastCheckedAt,
            mentionsCount: account._count.mentions,
          })),
        };
      } catch (error) {
        console.error('Get monitored accounts error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch monitored accounts',
        });
      }
    }),

  /**
   * Add a new Twitter account to monitor
   */
  add: protectedProcedure
    .input(z.object({
      handle: z.string().min(1, 'Twitter handle is required'),
    }))
    .mutation(async ({ input, ctx }) => {
      console.log('🚀 Starting account addition process for:', input.handle);
      console.log('👤 User ID:', ctx.userId);

      try {
        // Check if user can add more monitored accounts
        console.log('🔍 Checking feature limits for MONITORED_ACCOUNTS...');
        const featureCheck = await canUserUseFeature(ctx.userId!, 'MONITORED_ACCOUNTS');
        console.log('📊 Feature check result:', featureCheck);

        if (!featureCheck.allowed) {
          console.log('❌ Feature limit exceeded:', featureCheck);
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: `You've reached your limit of ${featureCheck.limit} monitored accounts. Upgrade your plan to monitor more accounts.`,
          });
        }

        // Clean and validate the handle
        console.log('🧹 Cleaning handle:', input.handle);
        const cleanHandle = input.handle.replace('@', '').toLowerCase();
        console.log('✨ Clean handle:', cleanHandle);

        if (!twitterClient.validateTwitterHandle(cleanHandle)) {
          console.log('❌ Invalid handle format:', cleanHandle);
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid Twitter handle format. Handles must be 1-15 characters, alphanumeric and underscores only.',
          });
        }
        console.log('✅ Handle validation passed');

        // Check if account is already being monitored
        console.log('🔍 Checking for existing account...');
        const existingAccount = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            userId: ctx.userId!,
            twitterHandle: cleanHandle,
          },
        });
        console.log('📋 Existing account check result:', existingAccount ? 'Found' : 'Not found');

        if (existingAccount) {
          console.log('❌ Account already monitored:', cleanHandle);
          throw new TRPCError({
            code: 'CONFLICT',
            message: `You're already monitoring @${cleanHandle}`,
          });
        }

        // Fetch user info from Twitter to validate account exists
        console.log('🐦 Fetching Twitter user info for:', cleanHandle);
        let userInfo;
        try {
          const response = await twitterClient.getUserInfo(cleanHandle);
          userInfo = response;
          console.log('✅ Twitter user info fetched successfully:', {
            id: userInfo.id,
            userName: userInfo.userName,
            name: userInfo.name
          });
        } catch (error) {
          console.error('❌ Twitter API error:', error);
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: `Twitter account @${cleanHandle} not found or is private`,
          });
        }

        // Create the monitored account
        console.log('💾 Creating monitored account in database...');
        const accountData = {
          userId: ctx.userId!,
          twitterHandle: cleanHandle,
          twitterId: userInfo.id,
          displayName: userInfo.name || cleanHandle,
          avatarUrl: userInfo.profilePicture,
          isActive: true,
          lastCheckedAt: new Date(),
        };
        console.log('📝 Account data to create:', accountData);

        const account = await ctx.prisma.monitoredAccount.create({
          data: accountData,
        });
        console.log('✅ Account created successfully:', account.id);

        // Log usage
        console.log('📊 Logging usage for MONITORED_ACCOUNTS...');
        await logUsage(ctx.userId!, 'MONITORED_ACCOUNTS', 1);
        console.log('✅ Usage logged successfully');

        const result = {
          success: true,
          account: {
            id: account.id,
            handle: account.twitterHandle,
            displayName: account.displayName,
            avatar: account.avatarUrl,
            isActive: account.isActive,
            platform: 'twitter',
            followerCount: 0,
            createdAt: account.createdAt,
            lastSyncAt: account.lastCheckedAt,
            mentionsCount: 0,
          },
          message: `Now monitoring @${cleanHandle}`,
        };
        console.log('🎉 Account addition completed successfully:', result);
        return result;
      } catch (error) {
        console.error('❌ Add monitored account error:', error);

        if (error instanceof TRPCError) {
          console.error('🔴 TRPCError details:', {
            code: error.code,
            message: error.message,
            cause: error.cause
          });
          throw error;
        }

        console.error('🔴 Unexpected error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to add monitored account',
        });
      }
    }),

  /**
   * Remove a monitored account
   */
  remove: protectedProcedure
    .input(z.object({
      accountId: z.string().cuid('Invalid account ID'),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the account belongs to the user
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Monitored account not found',
          });
        }

        // Delete the account and all related mentions
        await ctx.prisma.monitoredAccount.delete({
          where: {
            id: input.accountId,
          },
        });

        return {
          success: true,
          message: `Stopped monitoring @${account.twitterHandle}`,
        };
      } catch (error) {
        console.error('Remove monitored account error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to remove monitored account',
        });
      }
    }),

  /**
   * Toggle monitoring status for an account
   */
  toggleStatus: protectedProcedure
    .input(z.object({
      accountId: z.string().cuid('Invalid account ID'),
      isActive: z.boolean(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the account belongs to the user
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Monitored account not found',
          });
        }

        // Update the status
        const updatedAccount = await ctx.prisma.monitoredAccount.update({
          where: {
            id: input.accountId,
          },
          data: {
            isActive: input.isActive,
          },
        });

        return {
          success: true,
          account: updatedAccount,
          message: `@${account.twitterHandle} monitoring ${input.isActive ? 'enabled' : 'disabled'}`,
        };
      } catch (error) {
        console.error('Toggle account status error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update account status',
        });
      }
    }),
});