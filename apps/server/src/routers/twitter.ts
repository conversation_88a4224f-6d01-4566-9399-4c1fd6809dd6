/**
 * Twitter Router for BuddyChip tRPC API
 * 
 * Handles Twitter/X integration operations including:
 * - Extracting tweet content from URLs
 * - Fetching user mentions and tweet replies
 * - Validating Twitter handles and URLs
 * - Managing Twitter data for the dashboard
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { protectedProcedure, createTRPCRouter } from '../lib/trpc';
import { twitterClient } from '../lib/twitter-client';

export const twitterRouter = createTRPCRouter({
  /**
   * Extract tweet content from a Twitter/X URL
   * Used for the Quick Reply feature
   */
  extractTweetContent: protectedProcedure
    .input(z.object({
      url: z.string().url('Please provide a valid URL'),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Validate it's a Twitter URL
        if (!twitterClient.validateTwitterUrl(input.url)) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Please provide a valid Twitter or X.com URL',
          });
        }

        // Extract tweet content
        const tweet = await twitterClient.getTweetFromUrl(input.url);
        
        if (!tweet) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Could not fetch tweet content. The tweet may be private, deleted, or the URL is invalid.',
          });
        }

        // Store the extracted tweet in our database for reference
        const mention = await ctx.prisma.mention.create({
          data: {
            id: tweet.id, // Use tweet ID as primary key
            userId: ctx.userId!,
            link: twitterClient.normalizeTwitterUrl(input.url),
            content: tweet.text,
            authorHandle: tweet.author.userName,
            authorName: tweet.author.name,
            authorAvatarUrl: tweet.author.profilePicture,
            mentionedAt: new Date(tweet.createdAt),
            processed: false,
          },
        });

        return {
          success: true,
          tweet: {
            id: tweet.id,
            text: tweet.text,
            author: {
              userName: tweet.author.userName,
              name: tweet.author.name,
              profilePicture: tweet.author.profilePicture,
            },
            created_at: tweet.createdAt,
            url: twitterClient.normalizeTwitterUrl(input.url),
            metrics: {
              retweet_count: tweet.retweetCount || 0,
              like_count: tweet.likeCount || 0,
              reply_count: tweet.replyCount || 0,
              quote_count: tweet.quoteCount || 0,
            },
          },
          mentionId: mention.id,
        };
      } catch (error) {
        console.error('Extract tweet content error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to extract tweet content. Please check the URL and try again.',
        });
      }
    }),

  /**
   * Validate a Twitter URL format
   */
  validateUrl: protectedProcedure
    .input(z.object({
      url: z.string(),
    }))
    .query(({ input }) => {
      const isValid = twitterClient.validateTwitterUrl(input.url);
      const tweetId = isValid ? twitterClient.extractTweetId(input.url) : null;
      const username = isValid ? twitterClient.extractUsername(input.url) : null;
      
      return {
        isValid,
        tweetId,
        username,
        normalizedUrl: isValid ? twitterClient.normalizeTwitterUrl(input.url) : null,
      };
    }),

  /**
   * Get user information from Twitter handle
   */
  getUserInfo: protectedProcedure
    .input(z.object({
      username: z.string().min(1, 'Username is required'),
    }))
    .query(async ({ input }) => {
      try {
        // Validate handle format
        const cleanUsername = input.username.replace('@', '');
        if (!twitterClient.validateTwitterHandle(cleanUsername)) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid Twitter handle format. Handles must be 1-15 characters, alphanumeric and underscores only.',
          });
        }

        const userInfo = await twitterClient.getUserInfo(cleanUsername);
        
        return {
          success: true,
          user: userInfo,
        };
      } catch (error) {
        console.error('Get user info error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found or profile is private',
        });
      }
    }),

  /**
   * Fetch user mentions from Twitter API
   * Used for syncing mentions for monitored accounts
   */
  fetchUserMentions: protectedProcedure
    .input(z.object({
      username: z.string().min(1, 'Username is required'),
      cursor: z.string().optional(),
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ input }) => {
      try {
        const cleanUsername = input.username.replace('@', '');
        if (!twitterClient.validateTwitterHandle(cleanUsername)) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid Twitter handle format',
          });
        }

        const mentions = await twitterClient.getUserMentions(cleanUsername, {
          cursor: input.cursor,
          limit: input.limit,
        });

        return {
          success: true,
          data: mentions.tweets,
          nextCursor: mentions.next_cursor,
          resultCount: mentions.tweets.length,
        };
      } catch (error) {
        console.error('Fetch user mentions error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch mentions from Twitter',
        });
      }
    }),

  /**
   * Fetch replies to a specific tweet
   */
  fetchTweetReplies: protectedProcedure
    .input(z.object({
      tweetId: z.string().min(1, 'Tweet ID is required'),
      cursor: z.string().optional(),
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ input }) => {
      try {
        const replies = await twitterClient.getTweetReplies(input.tweetId, {
          cursor: input.cursor,
          limit: input.limit,
        });

        return {
          success: true,
          data: replies.tweets || [],
          nextCursor: replies.next_cursor || '',
          resultCount: replies.tweets?.length || 0,
        };
      } catch (error) {
        console.error('Fetch tweet replies error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch replies from Twitter',
        });
      }
    }),

  /**
   * Search tweets by keyword (if supported by TwitterAPI.io)
   */
  searchTweets: protectedProcedure
    .input(z.object({
      query: z.string().min(1, 'Search query is required'),
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ input }) => {
      // This would need to be implemented based on TwitterAPI.io's search capabilities
      // For now, return empty results
      return {
        success: true,
        data: [],
        message: 'Tweet search will be implemented when TwitterAPI.io search endpoint is available',
      };
    }),

  /**
   * Get trending topics (if supported by TwitterAPI.io)
   */
  getTrending: protectedProcedure
    .query(async () => {
      // This would need to be implemented based on TwitterAPI.io's trending capabilities
      // For now, return empty results
      return {
        success: true,
        data: [],
        message: 'Trending topics will be implemented when TwitterAPI.io trending endpoint is available',
      };
    }),

  /**
   * Normalize Twitter URL (convert between twitter.com and x.com)
   */
  normalizeUrl: protectedProcedure
    .input(z.object({
      url: z.string().url(),
    }))
    .query(({ input }) => {
      if (!twitterClient.validateTwitterUrl(input.url)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid Twitter URL',
        });
      }

      return {
        original: input.url,
        normalized: twitterClient.normalizeTwitterUrl(input.url),
        isValid: true,
      };
    }),

  /**
   * Clear Twitter API cache (for testing/debugging)
   */
  clearCache: protectedProcedure
    .mutation(() => {
      twitterClient.clearCache();
      return {
        success: true,
        message: 'Twitter API cache cleared',
      };
    }),

  /**
   * Get Twitter API cache statistics
   */
  getCacheStats: protectedProcedure
    .query(() => {
      const stats = twitterClient.getCacheStats();
      return {
        success: true,
        cache: stats,
      };
    }),
});