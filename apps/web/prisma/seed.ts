/**
 * Seed script for BuddyChip database
 * 
 * This script populates the database with:
 * - Default subscription plans (<PERSON><PERSON>, <PERSON>ly God, Team Plan)
 * - Plan features and limits
 */

import { PrismaClient, FeatureType } from '../prisma/generated/index.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create subscription plans
  const replyGuyPlan = await prisma.subscriptionPlan.upsert({
    where: { name: 'reply-guy' },
    update: {},
    create: {
      name: 'reply-guy',
      displayName: 'Reply Guy',
      description: 'Perfect for individuals starting their social media engagement journey',
      price: 20.00,
      baseUsers: 1,
      additionalUserPrice: null,
      isActive: true,
    },
  });

  const replyGodPlan = await prisma.subscriptionPlan.upsert({
    where: { name: 'reply-god' },
    update: {},
    create: {
      name: 'reply-god',
      displayName: 'Reply God',
      description: 'Advanced features for power users and content creators',
      price: 50.00,
      baseUsers: 1,
      additionalUserPrice: null,
      isActive: true,
    },
  });

  const teamPlan = await prisma.subscriptionPlan.upsert({
    where: { name: 'team-plan' },
    update: {},
    create: {
      name: 'team-plan',
      displayName: 'Team Plan',
      description: 'Collaboration features for teams and agencies',
      price: 79.00,
      baseUsers: 1,
      additionalUserPrice: 50.00,
      isActive: true,
    },
  });

  console.log('✅ Created subscription plans');

  // Define feature limits for each plan
  const planFeatures = [
    // Reply Guy Plan Features
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.AI_CALLS,
      limit: 100, // 100 AI calls per month
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: 20, // 20 image generations per month
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 5, // 5 monitored accounts
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: 1000, // 1000 mentions per month
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 1, // 1GB storage
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 1, // 1 team member
    },

    // Reply God Plan Features
    {
      planId: replyGodPlan.id,
      feature: FeatureType.AI_CALLS,
      limit: 500, // 500 AI calls per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: 100, // 100 image generations per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 15, // 15 monitored accounts
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: 5000, // 5000 mentions per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 5, // 5GB storage
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 1, // 1 team member
    },

    // Team Plan Features
    {
      planId: teamPlan.id,
      feature: FeatureType.AI_CALLS,
      limit: 2000, // 2000 AI calls per month
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: 500, // 500 image generations per month
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 50, // 50 monitored accounts
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: 20000, // 20000 mentions per month
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 20, // 20GB storage
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 10, // 10 team members
    },
  ];

  // Create plan features
  for (const feature of planFeatures) {
    await prisma.planFeature.upsert({
      where: {
        planId_feature: {
          planId: feature.planId,
          feature: feature.feature,
        },
      },
      update: {
        limit: feature.limit,
      },
      create: feature,
    });
  }

  console.log('✅ Created plan features');

  // Log the created plans
  const plans = await prisma.subscriptionPlan.findMany({
    include: {
      features: true,
    },
  });

  console.log('\n📋 Created Plans:');
  plans.forEach(plan => {
    console.log(`\n${plan.displayName} ($${plan.price}/month):`);
    plan.features.forEach(feature => {
      const limitText = feature.limit === -1 ? 'Unlimited' : feature.limit.toString();
      console.log(`  - ${feature.feature}: ${limitText}`);
    });
  });

  console.log('\n🎉 Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });