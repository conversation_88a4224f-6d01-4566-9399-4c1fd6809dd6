{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "server/*": ["../server/src/*"]}}, "include": ["./**/*.ts", "./**/*.tsx", "./.next/types/**/*.ts", "./next-env.d.ts", ".next/types/**/*.ts", "../server/src/routers/index.ts"], "exclude": ["./node_modules"]}