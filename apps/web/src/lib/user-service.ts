/**
 * User Service for BuddyChip
 * 
 * Handles user operations and ensures users exist in database
 */

import { prisma } from './db-utils';
import type { User, SubscriptionPlan, PlanFeature } from '../../prisma/generated';

export type UserWithPlan = User & {
  plan: SubscriptionPlan & {
    features: PlanFeature[];
  };
};

/**
 * Get or create user in database (just-in-time creation)
 * Used as fallback when webhooks haven't synced yet
 */
export async function getOrCreateUser(clerkUserId: string, userData?: {
  email?: string;
  name?: string;
  avatar?: string;
}): Promise<UserWithPlan> {
  // Try to find existing user
  let user = await prisma.user.findUnique({
    where: { id: clerkUserId },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  });

  // If user doesn't exist, create them with default plan
  if (!user) {
    let defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'reply-guy' },
      include: {
        features: true,
      },
    });

    // If default plan doesn't exist, create it
    if (!defaultPlan) {
      console.log('⚠️ UserService: Default plan not found, creating it...');

      // Create the default plan
      defaultPlan = await prisma.subscriptionPlan.create({
        data: {
          name: 'reply-guy',
          displayName: 'Reply Guy',
          description: 'Perfect for individuals getting started with social media automation',
          price: 29.00,
          baseUsers: 1,
          isActive: true,
        },
        include: {
          features: true,
        },
      });

      // Create default features for the plan
      await prisma.planFeature.createMany({
        data: [
          {
            planId: defaultPlan.id,
            feature: 'MONITORED_ACCOUNTS',
            limit: 3,
          },
          {
            planId: defaultPlan.id,
            feature: 'MENTIONS_PER_SYNC',
            limit: 25,
          },
          {
            planId: defaultPlan.id,
            feature: 'MAX_TOTAL_MENTIONS',
            limit: 100,
          },
          {
            planId: defaultPlan.id,
            feature: 'AI_CALLS',
            limit: 1000,
          },
        ],
      });

      // Refetch the plan with features
      defaultPlan = await prisma.subscriptionPlan.findFirst({
        where: { name: 'reply-guy' },
        include: {
          features: true,
        },
      });

      console.log('✅ UserService: Created default plan with features');
    }

    if (!defaultPlan) {
      throw new Error('Failed to create or find default subscription plan');
    }

    user = await prisma.user.create({
      data: {
        id: clerkUserId,
        email: userData?.email || null,
        name: userData?.name || null,
        avatar: userData?.avatar || null,
        planId: defaultPlan.id,
        lastActiveAt: new Date(),
      },
      include: {
        plan: {
          include: {
            features: true,
          },
        },
      },
    });

    console.log(`✅ UserService: Just-in-time user creation: ${clerkUserId}`);
    console.log('📋 UserService: Created user with plan:', {
      planId: user.planId,
      planName: user.plan.name,
      features: user.plan.features.map(f => ({ feature: f.feature, limit: f.limit }))
    });
  } else {
    // Update last active time
    await prisma.user.update({
      where: { id: clerkUserId },
      data: { lastActiveAt: new Date() },
    });
  }

  return user;
}

/**
 * Get user by Clerk ID
 */
export async function getUserById(clerkUserId: string): Promise<UserWithPlan | null> {
  return prisma.user.findUnique({
    where: { id: clerkUserId },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  });
}

/**
 * Update user information
 */
export async function updateUser(clerkUserId: string, data: {
  email?: string;
  name?: string;
  avatar?: string;
}) {
  return prisma.user.update({
    where: { id: clerkUserId },
    data: {
      ...data,
      lastActiveAt: new Date(),
    },
  });
}

/**
 * Get user's current usage for a feature
 */
export async function getUserUsage(clerkUserId: string, feature: string, billingPeriod?: string) {
  const currentPeriod = billingPeriod || getCurrentBillingPeriod();
  
  const usage = await prisma.usageLog.aggregate({
    where: {
      userId: clerkUserId,
      feature: feature as any,
      billingPeriod: currentPeriod,
    },
    _sum: {
      amount: true,
    },
  });

  return usage._sum.amount || 0;
}

/**
 * Check if user can use a feature (rate limiting)
 */
export async function canUserUseFeature(clerkUserId: string, feature: string): Promise<{
  allowed: boolean;
  currentUsage: number;
  limit: number;
  resetDate?: Date;
}> {
  console.log('🔍 UserService: Checking feature usage for user:', clerkUserId, 'feature:', feature);

  const user = await getUserById(clerkUserId);
  if (!user) {
    console.error('❌ UserService: User not found:', clerkUserId);
    console.log('🔄 UserService: This should not happen with JIT user creation, but handling gracefully');
    // Return a restrictive default instead of throwing
    return {
      allowed: false,
      currentUsage: 0,
      limit: 0,
      resetDate: getNextBillingPeriodStart(),
    };
  }
  console.log('👤 UserService: User found:', { id: user.id, planId: user.planId });

  // Find feature limit for user's plan
  const planFeature = user.plan.features.find((f: PlanFeature) => f.feature === feature);
  console.log('📋 UserService: Available plan features:', user.plan.features.map(f => ({ feature: f.feature, limit: f.limit })));
  console.log('🎯 UserService: Target plan feature:', planFeature);

  if (!planFeature) {
    console.log('❌ UserService: Feature not found in plan:', feature);
    return { allowed: false, currentUsage: 0, limit: 0 };
  }

  const limit = planFeature.limit;
  console.log('📊 UserService: Feature limit:', limit);

  // -1 means unlimited
  if (limit === -1) {
    console.log('♾️ UserService: Unlimited feature access');
    return { allowed: true, currentUsage: 0, limit: -1 };
  }

  const currentUsage = await getUserUsage(clerkUserId, feature);
  const allowed = currentUsage < limit;
  console.log('📈 UserService: Usage check:', { currentUsage, limit, allowed });

  const result = {
    allowed,
    currentUsage,
    limit,
    resetDate: getNextBillingPeriodStart(),
  };
  console.log('✅ UserService: Feature check result:', result);
  return result;
}

/**
 * Log feature usage
 */
export async function logUsage(clerkUserId: string, feature: string, amount = 1, metadata?: any) {
  return prisma.usageLog.create({
    data: {
      userId: clerkUserId,
      feature: feature as any,
      amount,
      metadata,
      billingPeriod: getCurrentBillingPeriod(),
    },
  });
}

/**
 * Get current billing period (YYYY-MM format)
 */
function getCurrentBillingPeriod(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
}

/**
 * Get start of next billing period
 */
function getNextBillingPeriodStart(): Date {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth() + 1, 1);
}

/**
 * Get user dashboard statistics
 */
export async function getUserStats(clerkUserId: string) {
  const [monitoredAccountsCount, mentionsCount, aiResponsesCount, currentUsage] = await Promise.all([
    // Monitored accounts count
    prisma.monitoredAccount.count({
      where: { userId: clerkUserId, isActive: true },
    }),
    
    // Recent mentions count (this week)
    prisma.mention.count({
      where: {
        OR: [
          { userId: clerkUserId },
          { account: { userId: clerkUserId } },
        ],
        createdAt: {
          gte: getStartOfWeek(),
        },
      },
    }),
    
    // AI responses count (total)
    prisma.aIResponse.count({
      where: { userId: clerkUserId },
    }),
    
    // Current month AI usage
    getUserUsage(clerkUserId, 'AI_CALLS'),
  ]);

  return {
    monitoredAccounts: monitoredAccountsCount,
    recentMentions: mentionsCount,
    aiResponses: aiResponsesCount,
    aiCallsThisMonth: currentUsage,
  };
}

/**
 * Get start of current week
 */
function getStartOfWeek(): Date {
  const now = new Date();
  const day = now.getDay();
  const diff = now.getDate() - day;
  return new Date(now.setDate(diff));
}