/**
 * AI Provider Configuration for BuddyChip
 * 
 * Configures OpenRouter and OpenAI providers for the Benji AI agent
 */

import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { openai } from '@ai-sdk/openai';
import { getSubscriptionPlan } from './subscriptions';

// Model configurations
export const modelConfig = {
  gemini25Flash: {
    provider: 'openrouter',
    modelId: 'google/gemini-2.5-flash',
  },
  gemini25Pro: {
    provider: 'openrouter',
    modelId: 'google/gemini-2.5-pro',
  },
  openaiO3: {
    provider: 'openai',
    modelId: 'o1-mini',
  },
} as const;

export type ModelName = keyof typeof modelConfig;

// Get model instance by name
export function getModel(modelName: ModelName): any {
  const config = modelConfig[modelName];
  
  if (config.provider === 'openrouter') {
    const provider = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY!,
    });
    return provider(config.modelId);
  } else if (config.provider === 'openai') {
    return openai(config.modelId);
  } else {
    throw new Error(`Unknown provider: ${(config as any).provider}`);
  }
}

// Model metadata for UI display
export const modelMetadata = {
  gemini25Flash: {
    name: 'Gemini 2.5 Flash',
    description: 'Fast and efficient for general tasks',
    provider: 'Google (OpenRouter)',
    costTier: 'low',
    speed: 'fast',
  },

  gemini25Pro: {
    name: 'Gemini 2.5 Pro',
    description: 'Advanced reasoning and complex tasks',
    provider: 'Google (OpenRouter)',
    costTier: 'medium',
    speed: 'medium',
  },

  openaiO3: {
    name: 'OpenAI o1-mini',
    description: 'Latest reasoning model',
    provider: 'OpenAI',
    costTier: 'high',
    speed: 'slow',
  },
} as const;

// Get model for subscription plan
export function getModelByPlan(planName: string): ModelName {
  const plan = getSubscriptionPlan(planName);
  return plan ? plan.defaultModel : 'gemini25Flash';
}

// Validate environment variables
export function validateAIEnvironment() {
  const required = [
    'OPENROUTER_API_KEY',
    'OPENAI_API_KEY',
  ];
  
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  return true;
}